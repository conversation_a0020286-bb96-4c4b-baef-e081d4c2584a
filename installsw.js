function loadCS(progress, msg = 'Cargando Recursos...') {
    if (document.getElementById('span_msg_load') != null) {
        document.getElementById('span_msg_load').textContent = msg;
        document.getElementById('span_num_por').textContent = progress + '%';
        document.getElementById('div_porcentage').style.width = progress + '%';
    }
}

let callbackOnActiveExecuted = false; // Bandera para asegurar que el callback se ejecuta una sola vez
let messageListenerAttached = false; // Bandera para asegurar que el listener se añade una sola vez

function executeCallbackOnActiveOnce(callback) {
    if (!callbackOnActiveExecuted && callback) {
        console.log("Ejecutando callbackOnActive por primera vez.");
        callback();
        callbackOnActiveExecuted = true;
    } else if (callbackOnActiveExecuted && callback) {
        console.log("callbackOnActive ya fue ejecutado, omitiendo llamada.");
    }
}

function setupMessageListener(callbackOnActive = null) {
    if (messageListenerAttached) return;

    navigator.serviceWorker.addEventListener('message', event => {
        const data = event.data;
        if (!data) return;

        if (data.type === 'progress') loadCS(data.progress, data.msg);
        if (data.type === 'CACHE_COMPLETE') loadCS(100, 'Cache Completado');
        if (data.type === 'ACTIVATED') {
            console.log('SW activado (mensaje recibido de versión:', data.version, ')'); // data.version viene del SW
            loadCS(100, 'Service Worker Activado');
            if (!navigator.serviceWorker.installing) {
                executeCallbackOnActiveOnce(callbackOnActive);
            } else {
                console.log('SW activado (mensaje), pero una nueva actualización (navigator.serviceWorker.installing) ya está en curso. Callback no ejecutado desde ACTIVATED.');
            }
        }
        if (data.type === 'CACHE_FAILED') loadCS(100, 'Error de Cache: ' + data.msg); // Asegurar 100% en error
        if (data.type === 'UPDATE_FAILED') loadCS(100, 'Error de Actualización: ' + data.msg); // Asegurar 100% en error
    });
    messageListenerAttached = true;
}

function installSW(callbackOnActive = null) {
    if ('serviceWorker' in navigator) {
        setupMessageListener(callbackOnActive);

        const controller = navigator.serviceWorker.controller;

        if (!controller) {
            loadCS(25, 'Iniciando Service Worker...');
        }

        navigator.serviceWorker.register('sw.js', { scope: './' })
            .then(registration => {
                console.log('SW registrado con éxito:', registration);
                loadCS(50, 'Cacheando Recursos...');

                if (registration.installing) {
                    console.log('Service Worker instalándose.');
                    if (!controller) {
                        loadCS(50, 'Cacheando Recursos Iniciales...');
                    } else {
                        loadCS(50, 'Actualizando aplicación...');
                    }

                    registration.installing.addEventListener('statechange', function () {
                        if (this.state === 'installed') {
                            if (registration.waiting) {
                                console.log('Nuevo SW instalado y esperando para activar (actualización lista).');
                            } else {
                                console.log('Nuevo SW instalado, se activará.');
                            }
                        }
                    });
                } else if (registration.waiting) {
                    console.log('Hay un Service Worker esperando para activar (actualización lista).');
                } else if (registration.active) {
                    console.log('Service Worker ya está activo (encontrado/confirmado por registro).');
                }
            })
            .catch(err => {
                console.error('Error al registrar SW:', err);
                loadCS(50, 'Error al registrar el Service Worker');
            });

        window.addEventListener('beforeinstallprompt', e => e.preventDefault());
        window.addEventListener('appinstalled', () => console.log('PWA instalada'));

        return navigator.serviceWorker.getRegistration();
    } else {
        alert('Este navegador no soporta PWA. Usa otro navegador o actualízalo.');
    }
}